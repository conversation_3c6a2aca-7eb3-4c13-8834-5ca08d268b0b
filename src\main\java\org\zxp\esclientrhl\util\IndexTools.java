package org.zxp.esclientrhl.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.zxp.esclientrhl.config.ElasticsearchProperties;
import org.zxp.esclientrhl.config.EntityConfig;
import org.zxp.esclientrhl.config.MappingConfig;
import org.zxp.esclientrhl.config.MetaDataConfig;
import org.zxp.esclientrhl.enums.DataType;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * 索引信息操作工具类 - 改造为支持配置对象
 *
 * 功能说明：
 * - 去掉注解依赖，改为使用EntityConfig配置
 * - 保留所有原有的索引操作功能
 * - 支持通过配置生成MetaData和MappingData
 *
 * <AUTHOR> zhang
 * @since 2019-01-29
 */
@Component
public class IndexTools {
    @Autowired
    private ElasticsearchProperties elasticsearchProperties;

    ///**
    // * 获取索引元数据：indexname、indextype
    // * @param clazz
    // * @return
    // */
    //public static MetaData getIndexType(Class<?> clazz){
    //    String indexname = "";
    //    String indextype = "";
    //    if(clazz.getAnnotation(ESMetaData.class) != null){
    //        indexname = clazz.getAnnotation(ESMetaData.class).indexName();
    //        indextype = clazz.getAnnotation(ESMetaData.class).indexType();
    //        if(indextype == null || indextype.equals("")){indextype = "_doc";}
    //        MetaData metaData = new MetaData(indexname,indextype);
    //        metaData.setPrintLog(clazz.getAnnotation(ESMetaData.class).printLog());
    //        if(Tools.arrayISNULL(clazz.getAnnotation(ESMetaData.class).searchIndexNames())) {
    //            metaData.setSearchIndexNames(new String[]{indexname});
    //        }else{
    //            metaData.setSearchIndexNames((clazz.getAnnotation(ESMetaData.class).searchIndexNames()));
    //        }
    //        return metaData;
    //    }
    //    return null;
    //}
    //
    ///**
    // * 获取索引元数据：主分片、备份分片数的配置
    // * @param clazz
    // * @return
    // */
    //public static MetaData getShardsConfig(Class<?> clazz){
    //    int number_of_shards = 0;
    //    int number_of_replicas = 0;
    //    if(clazz.getAnnotation(ESMetaData.class) != null){
    //        number_of_shards = clazz.getAnnotation(ESMetaData.class).number_of_shards();
    //        number_of_replicas = clazz.getAnnotation(ESMetaData.class).number_of_replicas();
    //        MetaData metaData = new MetaData(number_of_shards,number_of_replicas);
    //        metaData.setPrintLog(clazz.getAnnotation(ESMetaData.class).printLog());
    //        return metaData;
    //    }
    //    return null;
    //}

    /**
     * 根据枚举类型获得mapping中的类型
     */
    private static String getType(DataType dataType) {
        return dataType.toString().replaceAll("_type", "");
    }

    /**
     * 根据EntityConfig获取索引元数据（新方法）
     * @param entityConfig 实体配置
     * @return MetaData
     */
    public MetaData getMetaData(EntityConfig entityConfig) {
        if (entityConfig == null || entityConfig.getMetaData() == null) {
            throw new IllegalArgumentException("EntityConfig或MetaDataConfig不能为空");
        }

        MetaDataConfig config = entityConfig.getMetaData();
        String indexname = config.getIndexName();
        String indextype = config.getIndexType();

        // ES7+ 默认使用_doc
        if (indextype == null || indextype.equals("")) {
            indextype = "_doc";
        }

        MetaData metaData = new MetaData(indexname, indextype,
            config.getNumberOfShards(), config.getNumberOfReplicas());

        // 如果配置了Suffix则自动添加后缀到索引名称
        if (config.isSuffix()) {
            metaData.setSuffix(elasticsearchProperties.getSuffix());
            if (metaData.getSuffix() != null && !"".equals(metaData.getSuffix())) {
                metaData.setIndexname(metaData.getIndexname() + "_" + metaData.getSuffix());
                indexname = metaData.getIndexname();
            }
        }

        metaData.setPrintLog(config.isPrintLog());

        // 设置搜索索引名称
        if (Tools.arrayISNULL(config.getSearchIndexNames())) {
            metaData.setSearchIndexNames(new String[]{indexname});
        } else {
            metaData.setSearchIndexNames(config.getSearchIndexNames());
        }

        // 设置别名相关配置
        metaData.setAlias(config.isAlias());
        metaData.setAliasIndex(config.getAliasIndex());
        metaData.setWriteIndex(config.getWriteIndex());

        // 设置滚动索引相关配置
        metaData.setRollover(config.isRollover());
        metaData.setRolloverMaxIndexAgeCondition(config.getRolloverMaxIndexAgeCondition());
        metaData.setRolloverMaxIndexAgeTimeUnit(config.getRolloverMaxIndexAgeTimeUnit());
        metaData.setRolloverMaxIndexDocsCondition(config.getRolloverMaxIndexDocsCondition());
        metaData.setRolloverMaxIndexSizeCondition(config.getRolloverMaxIndexSizeCondition());
        metaData.setRolloverMaxIndexSizeByteSizeUnit(config.getRolloverMaxIndexSizeByteSizeUnit());

        // 设置其他配置
        metaData.setMaxResultWindow(config.getMaxResultWindow());
        metaData.setAutoRollover(config.isAutoRollover());
        metaData.setAutoCreateIndex(config.isAutoCreateIndex());
        metaData.setIsScore(config.isScore());

        // 设置settings路径
        if (StringUtils.isEmpty(config.getSettingsPath())) {
            metaData.setSettingsPath(metaData.getIndexname() + ".essettings");
        } else {
            metaData.setSettingsPath(config.getSettingsPath());
        }

        return metaData;
    }

    /**
     * 获取索引元数据：indexname、indextype、主分片、备份分片数的配置（已删除注解支持）
     * @deprecated 注解功能已移除，请使用 getMetaData(EntityConfig entityConfig)
     * @param clazz
     * @return
     */
    @Deprecated
    public MetaData getMetaData(Class<?> clazz) {
        throw new UnsupportedOperationException("注解功能已移除，请使用 getMetaData(EntityConfig entityConfig) 方法");
    }

    /**
     * 获取字段映射数据（支持EntityConfig和注解两种方式）
     * @param field 字段
     * @param entityConfig 实体配置（可选）
     * @return 映射数据
     */
    public MappingData getMappingData(Field field, EntityConfig entityConfig) {
        if (field == null) {
            return null;
        }
        field.setAccessible(true);
        MappingData mappingData = new MappingData();
        mappingData.setField_name(field.getName());

        // 优先使用EntityConfig方式
        if (entityConfig != null && entityConfig.hasFieldMapping(field.getName())) {
            MappingConfig mappingConfig = entityConfig.getFieldMapping(field.getName());
            return createMappingDataFromConfig(field, mappingConfig);
        }

        // 回退到注解方式（注解已删除，使用默认逻辑）
        return createMappingDataByDefault(field);
    }

    /**
     * 获取配置于Field上的mapping信息（原有方法，保持兼容性）
     * @param field 字段
     * @return 映射数据
     */
    public MappingData getMappingData(Field field) {
        return getMappingData(field, null);
    }

    /**
     * 根据MappingConfig创建MappingData
     */
    private MappingData createMappingDataFromConfig(Field field, MappingConfig mappingConfig) {
        MappingData mappingData = new MappingData();
        mappingData.setField_name(field.getName());
        mappingData.setDatatype(getType(mappingConfig.getDatatype()));
        mappingData.setAnalyzer(mappingConfig.getAnalyzer() != null ? mappingConfig.getAnalyzer().toString() : "");
        mappingData.setSearch_analyzer(mappingConfig.getSearchAnalyzer() != null ? mappingConfig.getSearchAnalyzer().toString() : "");
        mappingData.setNgram(mappingConfig.isNgram());
        mappingData.setIgnore_above(mappingConfig.getIgnoreAbove());
        mappingData.setKeyword(mappingConfig.isKeyword());
        mappingData.setSuggest(mappingConfig.isSuggest());
        mappingData.setAllow_search(mappingConfig.isAllowSearch());
        mappingData.setCopy_to(mappingConfig.getCopyTo());
        mappingData.setNull_value(mappingConfig.getNullValue());
        mappingData.setDateFormat(mappingConfig.getDateFormat());
        mappingData.setNormalizer(mappingConfig.getNormalizer());
        return mappingData;
    }

    /**
     * 使用默认逻辑创建MappingData（注解已删除）
     */
    private MappingData createMappingDataByDefault(Field field) {
        MappingData mappingData = new MappingData();
        mappingData.setField_name(field.getName());

        // 注解已删除，使用默认的类型推断逻辑
        mappingData.setKeyword(false);

        if (field.getType() == String.class) {
            mappingData.setDatatype(getType(DataType.text_type));
            mappingData.setKeyword(true);
        } else if (field.getType() == Short.class || field.getType() == short.class) {
            mappingData.setDatatype(getType(DataType.short_type));
        } else if (field.getType() == Integer.class || field.getType() == int.class) {
            mappingData.setDatatype(getType(DataType.integer_type));
        } else if (field.getType() == Long.class || field.getType() == long.class) {
            mappingData.setDatatype(getType(DataType.long_type));
        } else if (field.getType() == Float.class || field.getType() == float.class) {
            mappingData.setDatatype(getType(DataType.float_type));
        } else if (field.getType() == Double.class || field.getType() == double.class) {
            mappingData.setDatatype(getType(DataType.double_type));
        } else if (field.getType() == BigDecimal.class) {
            mappingData.setDatatype(getType(DataType.double_type));
        } else if (field.getType() == Boolean.class || field.getType() == boolean.class) {
            mappingData.setDatatype(getType(DataType.boolean_type));
        } else if (field.getType() == Byte.class || field.getType() == byte.class) {
            mappingData.setDatatype(getType(DataType.byte_type));
        } else if (field.getType() == Date.class) {
            mappingData.setDatatype(getType(DataType.date_type));
        } else {
            mappingData.setDatatype(getType(DataType.text_type));
            mappingData.setKeyword(true);
        }

        // 设置默认值
        mappingData.setAnalyzer("standard");
        mappingData.setNgram(false);
        mappingData.setIgnore_above(256);
        mappingData.setSearch_analyzer("standard");
        mappingData.setSuggest(false);
        mappingData.setAllow_search(true);
        mappingData.setCopy_to("");
        mappingData.setNested_class(null);
        return mappingData;
    }

    /**
     * 批量获取配置于Field上的mapping信息，如果未配置注解，则给出默认信息
     *
     * @param clazz
     * @return
     */
    public MappingData[] getMappingData(Class<?> clazz) {
        Field[] fields = clazz.getDeclaredFields();
        MappingData[] mappingDataList = new MappingData[fields.length];
        for (int i = 0; i < fields.length; i++) {
            if (fields[i].getName().equals("serialVersionUID")) {
                continue;
            }
            mappingDataList[i] = getMappingData(fields[i]);
        }
        return mappingDataList;
    }
}
