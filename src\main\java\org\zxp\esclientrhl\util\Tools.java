package org.zxp.esclientrhl.util;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.zxp.esclientrhl.config.EntityConfig;
import org.zxp.esclientrhl.enums.DataType;

import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 工具类 -支持配置对象
 *
 * 功能说明：
 * - 使用EntityConfig配置
 * - 支持通过配置获取ID字段值

 */
public class Tools {

    /**
     * 根据配置获取对象的ID字段值
     * @param obj 对象实例
     * @param entityConfig 实体配置
     * @return ID字段值
     */
    public static String getESId(Object obj, EntityConfig entityConfig) throws Exception {
        if (entityConfig == null || !entityConfig.hasIdField()) {
            return null;
        }

        String idFieldName = entityConfig.getIdField();
        Field field = obj.getClass().getDeclaredField(idFieldName);
        field.setAccessible(true);
        Object value = field.get(obj);

        return value != null ? value.toString() : null;
    }

    /**
     * 根据对象中的注解获取ID的字段值（已删除注解支持）
     * @deprecated 注解功能已移除，请使用 getESId(Object obj, EntityConfig entityConfig)
     */
    @Deprecated
    public static String getESId(Object obj) throws Exception {
        throw new UnsupportedOperationException("注解功能已移除，请使用 getESId(Object obj, EntityConfig entityConfig) 方法");
    }

    /**
     * 获取o中所有的字段有值的map组合
     * @return
     */
    public static Map<String, Object> getFieldValue(Object o) throws IllegalAccessException {
        Map<String, Object> retMap = new HashMap<>();
        Field[] fs = o.getClass().getDeclaredFields();
        for(int i = 0;i < fs.length;i++){
            Field f = fs[i];
            f.setAccessible(true);
            if(f.get(o) != null){
                retMap.put(f.getName(),f.get(o) );
            }
        }
        return retMap;
    }

    /**
     * 获取o中所有的字段有值的map组合
     * @return
     */
    public static Map<Object, List<Object>> toMapByFieldValue(List<Object> list, Class<?> clazz, String fieldName) throws Exception {
        if(CollectionUtils.isEmpty(list))return null;
        Map<Object, List<Object>> retMap = new HashMap<>();
        for (Object o : list) {
            Object key = getFieldVal(o, clazz, fieldName);
            if(retMap.containsKey(key)){
                retMap.get(key).add(o);
            }else{
                List<Object> l = new ArrayList<>();
                l.add(o);
                retMap.put(key,l);
            }
        }
        return retMap;
    }


    public static Object getFieldVal(Object o, Class<?> clazz, String fieldName) throws Exception {
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(o);
    }

    /**
     * 通过反射,获得定义Class时声明的父类的范型参数的类型.
     *
     * @param clazz The class to introspect
     * @return the first generic declaration, or <code>Object.class</code> if cannot be determined
     */
    public static Class<?> getSuperClassGenricType(Class<?> clazz) {
        return getSuperClassGenricType(clazz, 0);
    }

    /**
     * 通过反射,获得定义Class时声明的父类的范型参数的类型. 如public BookManager extends GenricManager<Book>
     *
     * @param clazz clazz The class to introspect
     * @param index the Index of the generic ddeclaration,start from 0.
     */
    public static Class<?> getSuperClassGenricType(Class<?> clazz, int index)
            throws IndexOutOfBoundsException {
        Type genType = clazz.getGenericSuperclass();
        if (!(genType instanceof ParameterizedType)) {
            return Object.class;
        }
        Type[] params = ((ParameterizedType) genType).getActualTypeArguments();
        if (index >= params.length || index < 0) {
            return Object.class;
        }
        if (!(params[index] instanceof Class)) {
            return Object.class;
        }
        return (Class<?>) params[index];
    }

    public static String arraytostring(String[] strs){
        if(StringUtils.isEmpty(strs)){
            return "";
        }
        StringBuffer sb = new StringBuffer();
        Arrays.asList(strs).stream().forEach(str -> sb.append(str).append(" "));
        return sb.toString();
    }

    public static boolean arrayISNULL(Object[] objs){
        if(objs == null || objs.length == 0){
            return true;
        }
        boolean flag = false;
        for (int i = 0; i < objs.length; i++) {
            if(!StringUtils.isEmpty(objs[i])){
                flag = true;
            }
        }
        if(flag){
            return false;
        }else{
            return true;
        }
    }

    public static <T> List<List<T>> splitList(List<T> oriList,boolean isParallel){
        if(oriList.size() <=  Constant.BULK_COUNT){
            List<List<T>> splitList = new ArrayList<>();
            splitList.add(oriList);
            return splitList;
        }
        int limit = (oriList.size() + Constant.BULK_COUNT - 1) / Constant.BULK_COUNT;
        if(isParallel){
            return Stream.iterate(0, n -> n + 1).limit(limit).parallel().map(a -> oriList.stream().skip(a * Constant.BULK_COUNT).limit(Constant.BULK_COUNT).parallel().collect(Collectors.toList())).collect(Collectors.toList());
        }else{
            final List<List<T>> splitList = new ArrayList<>();
            Stream.iterate(0, n -> n + 1).limit(limit).forEach(i -> {
                splitList.add(oriList.stream().skip(i * Constant.BULK_COUNT ).limit(Constant.BULK_COUNT ).collect(Collectors.toList()));
            });
            return splitList;
        }
    }

    /**
     * 判断当前类是否包含nested字段
     */
    private static Map<Class<?>, Boolean> checkNested = new HashMap<>();

    public static boolean checkNested(List<Object> list){
        if(list == null || list.size() == 0){
            return false;
        }
        return checkNested(list.get(0));
    }
    /**
     * 检查对象是否包含嵌套类型字段（支持EntityConfig和注解两种方式）
     * @param obj 对象实例
     * @param entityConfig 实体配置（可选）
     * @return 是否包含嵌套类型
     */
    public static boolean checkNested(Object obj, EntityConfig entityConfig){
        if(obj == null){
            return false;
        }

        // 优先使用EntityConfig方式
        if(entityConfig != null && entityConfig.hasFieldMappings()){
            for(String fieldName : entityConfig.getFieldMappings().keySet()){
                try {
                    Field f = obj.getClass().getDeclaredField(fieldName);
                    if(entityConfig.getFieldMapping(fieldName).getDatatype() == DataType.nested_type){
                        return true;
                    }
                } catch (NoSuchFieldException e) {
                    // 字段不存在，继续检查下一个
                }
            }
            return false;
        }

        // 回退到缓存检查（注解方式已废弃）
        if(checkNested.containsKey(obj.getClass())){
            return checkNested.get(obj.getClass());
        } else {
            // 注解已删除，默认返回false
            checkNested.put(obj.getClass(), false);
            return false;
        }
    }

    /**
     * 检查对象是否包含嵌套类型字段（原有方法，保持兼容性）
     * @param obj 对象实例
     * @return 是否包含嵌套类型
     */
    public static boolean checkNested(Object obj){
        return checkNested(obj, null);
    }
}
