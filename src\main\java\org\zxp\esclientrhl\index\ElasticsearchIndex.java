package org.zxp.esclientrhl.index;

import org.zxp.esclientrhl.config.EntityConfig;
import org.zxp.esclientrhl.util.MappingData;
import org.zxp.esclientrhl.util.MetaData;

import java.util.Map;

/**
 * Elasticsearch索引管理接口
 *
 * 功能说明：
 * - 提供ES索引的创建、删除、管理等操作
 * - 支持索引映射(Mapping)的管理
 * - 支持索引别名(Alias)的管理
 * - 支持索引滚动(Rollover)功能
 *
 * 主要功能：
 * 1. 索引生命周期管理：创建、删除、检查索引是否存在
 * 2. 映射管理：根据实体类注解自动生成映射
 * 3. 别名管理：创建和管理索引别名，支持写入索引切换
 * 4. 滚动索引：支持基于时间、大小、文档数量的索引滚动
 * 5. 元数据获取：获取索引的配置信息和映射信息
 *
 * 使用场景：
 * - 项目启动时自动创建索引
 * - 动态管理索引结构
 * - 实现索引的热切换和滚动
 * - 管理大数据量的时序索引
 *
 *  ES实体类型
 */
public interface ElasticsearchIndex<T> {

    // ==================== 新增：基于配置的方法 ====================

    /**
     * 创建索引（基于EntityConfig配置）
     * @param entityConfig 实体配置
     * @throws Exception
     */
    public void createIndex(EntityConfig entityConfig) throws Exception;

    /**
     * 切换Alias写入index（基于EntityConfig配置）
     * @param entityConfig 实体配置
     * @param writeIndex 写入索引名称
     * @throws Exception
     */
    public void switchAliasWriteIndex(EntityConfig entityConfig, String writeIndex) throws Exception;

    /**
     * 创建Alias（基于EntityConfig配置）
     * @param entityConfig 实体配置
     * @throws Exception
     */
    public void createAlias(EntityConfig entityConfig) throws Exception;

    /**
     * 删除索引（基于EntityConfig配置）
     * @param entityConfig 实体配置
     * @throws Exception
     */
    public void dropIndex(EntityConfig entityConfig) throws Exception;

    /**
     * 索引是否存在（基于EntityConfig配置）
     * @param entityConfig 实体配置
     * @return 是否存在
     * @throws Exception
     */
    public boolean exists(EntityConfig entityConfig) throws Exception;

    // ==================== 原有方法（保留兼容性） ====================

    /**
     * 创建索引
     * @deprecated 建议使用 createIndex(EntityConfig entityConfig)
     * @param clazz
     * @throws Exception
     */
    @Deprecated
    public void createIndex(Class<T> clazz) throws Exception;


    /**
     * 切换Alias写入index
     * @param clazz
     * @throws Exception
     */
    public void switchAliasWriteIndex(Class<T> clazz,String writeIndex) throws Exception;


    /**
     * 创建Alias
     * @param clazz
     * @throws Exception
     */
    public void createAlias(Class<T> clazz) throws Exception;

    /**
     * 创建索引
     * @param settings settings map信息
     * @param settingsList settings map信息（列表）
     * @param mappingJson mapping json
     * @param indexName 索引名称
     * @throws Exception
     */
    public void createIndex(Map<String,String> settings,Map<String,String[]> settingsList,String mappingJson,String indexName) throws Exception;
    /**
     * 删除索引
     * @param clazz
     * @throws Exception
     */
    public void dropIndex(Class<T> clazz) throws Exception;

    /**
     * 索引是否存在
     * @param clazz
     * @throws Exception
     */
    public boolean exists(Class<T> clazz) throws Exception;

    /**
     * 滚动索引
     * @param clazz
     * @param isAsyn 是否异步
     * @throws Exception
     */
    public void rollover(Class<T> clazz,boolean isAsyn) throws Exception;

    /**
     * 获得索引名称
     * @param clazz
     * @return
     */
    public String getIndexName(Class<T> clazz);

    /**
     * 获得MetaData配置
     * @param clazz
     * @return
     */
    public MetaData getMetaData(Class<T> clazz);
    /**
     * 获得MappingData配置
     * @param clazz
     * @return
     */
    public MappingData[] getMappingData(Class<T> clazz);
}
