package org.zxp.esclientrhl;

import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.zxp.esclientrhl.auto.intfproxy.ESRepository;
import org.zxp.esclientrhl.auto.intfproxy.RepositoryFactorySupport;
import org.zxp.esclientrhl.config.ElasticSearchConfiguration;
import org.zxp.esclientrhl.config.ElasticsearchProperties;
import org.zxp.esclientrhl.config.EntityConfig;
import org.zxp.esclientrhl.index.ElasticsearchIndex;
import org.zxp.esclientrhl.index.ElasticsearchIndexImpl;
import org.zxp.esclientrhl.repository.ElasticsearchTemplate;
import org.zxp.esclientrhl.repository.ElasticsearchTemplateImpl;
import org.zxp.esclientrhl.util.IndexTools;

/**
 * ES客户端工厂类 - 提供纯API调用方式
 * 
 * 功能说明：
 * - 提供工厂方法创建ES操作实例
 * - 支持Repository模式和Template模式
 * 
 * 使用方式：
 * ESClientFactory factory = new ESClientFactory("127.0.0.1:9200");
 * ElasticsearchTemplate template = factory.getElasticsearchTemplate();
 * ElasticsearchIndex indexManager = factory.getElasticsearchIndex();
 *
 */
public class ESClientFactory {
    
    private AnnotationConfigApplicationContext context;
    private RestHighLevelClient client;
    private ElasticsearchTemplate elasticsearchTemplate;
    private ElasticsearchIndex elasticsearchIndex;
    private IndexTools indexTools;
    private RepositoryFactorySupport repositoryFactory;
    
    /**
     * 构造函数 - 使用默认配置
     */
    public ESClientFactory(String hosts) {
        this(createDefaultProperties(hosts));
    }
    
    /**
     * 构造函数 - 使用自定义配置
     */
    public ESClientFactory(ElasticsearchProperties properties) {
        initializeContext(properties);
    }
    
    /**
     * 构造函数 - 使用现有的RestHighLevelClient
     */
    public ESClientFactory(RestHighLevelClient client) {
        this.client = client;
        initializeComponents();
    }
    
    /**
     * 创建默认配置
     */
    private static ElasticsearchProperties createDefaultProperties(String hosts) {
        ElasticsearchProperties properties = new ElasticsearchProperties();
        properties.setHost(hosts);
        properties.setMaxConnectTotal(30);
        properties.setMaxConnectPerRoute(10);
        properties.setConnectionRequestTimeoutMillis(2000);
        properties.setSocketTimeoutMillis(30000);
        properties.setConnectTimeoutMillis(2000);
        return properties;
    }
    
    /**
     * 初始化Spring上下文
     */
    private void initializeContext(ElasticsearchProperties properties) {
        context = new AnnotationConfigApplicationContext();
        
        // 注册配置类
        context.register(ESInternalConfiguration.class);
        
        // 注册属性Bean
        context.getBeanFactory().registerSingleton("elasticsearchProperties", properties);
        
        // 刷新上下文
        context.refresh();
        
        // 获取组件
        this.client = context.getBean(RestHighLevelClient.class);
        initializeComponents();
    }
    
    /**
     * 初始化ES组件
     */
    private void initializeComponents() {
        if (context != null) {
            this.elasticsearchTemplate = context.getBean(ElasticsearchTemplate.class);
            this.elasticsearchIndex = context.getBean(ElasticsearchIndex.class);
            this.indexTools = context.getBean(IndexTools.class);
            this.repositoryFactory = context.getBean(RepositoryFactorySupport.class);
        } else {
            // 手动创建组件（当使用外部RestHighLevelClient时）
            this.indexTools = new IndexTools();
            this.elasticsearchIndex = new ElasticsearchIndexImpl();
            this.elasticsearchTemplate = new ElasticsearchTemplateImpl();
            this.repositoryFactory = new RepositoryFactorySupport();
            
            // 设置依赖关系（需要通过反射或setter方法）
            setDependencies();
        }
    }
    
    /**
     * 设置组件依赖关系
     */
    private void setDependencies() {
        try {
            // 为ElasticsearchTemplateImpl设置client
            if (elasticsearchTemplate instanceof ElasticsearchTemplateImpl) {
                ElasticsearchTemplateImpl impl = (ElasticsearchTemplateImpl) elasticsearchTemplate;
                java.lang.reflect.Field clientField = impl.getClass().getDeclaredField("client");
                clientField.setAccessible(true);
                clientField.set(impl, client);
                
                java.lang.reflect.Field indexField = impl.getClass().getDeclaredField("elasticsearchIndex");
                indexField.setAccessible(true);
                indexField.set(impl, elasticsearchIndex);
                
                java.lang.reflect.Field indexToolsField = impl.getClass().getDeclaredField("indexTools");
                indexToolsField.setAccessible(true);
                indexToolsField.set(impl, indexTools);
            }
            
            // 为ElasticsearchIndexImpl设置client
            if (elasticsearchIndex instanceof ElasticsearchIndexImpl) {
                ElasticsearchIndexImpl impl = (ElasticsearchIndexImpl) elasticsearchIndex;
                java.lang.reflect.Field clientField = impl.getClass().getDeclaredField("client");
                clientField.setAccessible(true);
                clientField.set(impl, client);
                
                java.lang.reflect.Field indexToolsField = impl.getClass().getDeclaredField("indexTools");
                indexToolsField.setAccessible(true);
                indexToolsField.set(impl, indexTools);
            }
            
            // 为RepositoryFactorySupport设置依赖
            if (repositoryFactory != null) {
                java.lang.reflect.Field templateField = repositoryFactory.getClass().getDeclaredField("elasticsearchTemplate");
                templateField.setAccessible(true);
                templateField.set(repositoryFactory, elasticsearchTemplate);
            }
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to set dependencies", e);
        }
    }
    
    /**
     * 获取ElasticsearchTemplate实例
     */
    public <T, M> ElasticsearchTemplate<T, M> getElasticsearchTemplate() {
        return (ElasticsearchTemplate<T, M>) elasticsearchTemplate;
    }
    
    /**
     * 获取ElasticsearchIndex实例
     */
    public <T> ElasticsearchIndex<T> getElasticsearchIndex() {
        return (ElasticsearchIndex<T>) elasticsearchIndex;
    }
    
    /**
     * 获取IndexTools实例
     */
    public IndexTools getIndexTools() {
        return indexTools;
    }
    
    /**
     * 获取原生RestHighLevelClient
     */
    public RestHighLevelClient getClient() {
        return client;
    }
    
    /**
     * 创建Repository实例
     */
    public <T, M, R extends ESRepository<T, M>> R getRepository(Class<R> repositoryInterface, EntityConfig entityConfig) {
        if (repositoryFactory == null) {
            throw new RuntimeException("RepositoryFactory not available");
        }
        return repositoryFactory.createRepository(repositoryInterface, entityConfig);
    }
    
    /**
     * 关闭工厂和相关资源
     */
    public void close() {
        try {
            if (client != null) {
                client.close();
            }
            if (context != null) {
                context.close();
            }
        } catch (Exception e) {
            // 忽略关闭异常
        }
    }
    
    /**
     * 内部配置类
     */
    @Configuration
    static class ESInternalConfiguration extends ElasticSearchConfiguration {
        
        @Bean
        public IndexTools indexTools() {
            return new IndexTools();
        }
        
        @Bean
        public ElasticsearchIndex elasticsearchIndex() {
            return new ElasticsearchIndexImpl();
        }
        
        @Bean
        public ElasticsearchTemplate elasticsearchTemplate() {
            return new ElasticsearchTemplateImpl();
        }
        
        @Bean
        public RepositoryFactorySupport repositoryFactorySupport() {
            return new RepositoryFactorySupport();
        }
    }
}
