package org.zxp.esclientrhl.auto.intfproxy;

import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.framework.ProxyFactory;
import org.zxp.esclientrhl.config.EntityConfig;
import org.zxp.esclientrhl.repository.ElasticsearchTemplate;

/**
 * Repository工厂支持类 -支持EntityConfig
 *
 * 功能说明：
 * - 用于生成ESCRepository的代理bean
 * - 支持纯API调用方式
 */
public class RepositoryFactorySupport {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    private ElasticsearchTemplate elasticsearchTemplate;

    public RepositoryFactorySupport() {
    }

    public RepositoryFactorySupport(ElasticsearchTemplate elasticsearchTemplate) {
        this.elasticsearchTemplate = elasticsearchTemplate;
    }

    public void setElasticsearchTemplate(ElasticsearchTemplate elasticsearchTemplate) {
        this.elasticsearchTemplate = elasticsearchTemplate;
    }

    /**
     * 创建Repository代理实例（基于EntityConfig）
     */
    public <T, M, R extends ESRepository<T, M>> R createRepository(Class<R> repositoryInterface, EntityConfig entityConfig) {
        try {
            SimpleESRepository target = new SimpleESRepository();
            target.setElasticsearchTemplate(elasticsearchTemplate);
            target.setEntityConfig(entityConfig);

            ProxyFactory result = new ProxyFactory();
            result.setTarget(target);
            result.addAdvice(new MethodInterceptor() {
                @Override
                public Object invoke(MethodInvocation invocation) throws Throwable {
                    return invocation.proceed();
                }
            });
            result.setInterfaces(repositoryInterface, ESRepository.class);

            R repository = (R) result.getProxy(this.getClass().getClassLoader());
            return repository;
        } catch (Exception e) {
            logger.error("ESCRepository proxy create fail !", e);
            throw new RuntimeException("Failed to create repository proxy", e);
        }
    }


}
