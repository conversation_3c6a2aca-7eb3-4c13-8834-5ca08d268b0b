package org.zxp.esclientrhl.auto.intfproxy;

import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.Response;
import org.elasticsearch.index.query.QueryBuilder;
import org.zxp.esclientrhl.enums.AggsType;
import org.zxp.esclientrhl.repository.PageList;
import org.zxp.esclientrhl.repository.PageSortHighLight;

import java.util.List;
import java.util.Map;

public interface ESRepository<T,M> {

    /**
     * 通过Low Level REST Client 查询
     */
    public Response request(Request request) throws Exception;


    /**
     * 新增索引
     */
    public boolean save(T t) throws Exception;

    /**
     * 新增索引集合
     */
    public BulkResponse save(List<T> list) throws Exception;

    /**
     * 按照有值字段更新索引
     */
    public boolean update(T t) throws Exception;


    /**
     * 覆盖更新索引
     */
    public boolean updateCover(T t) throws Exception;


    /**
     * 删除索引
     */
    public boolean delete(T t) throws Exception;

    /**
     * 删除索引
     */
    public boolean deleteById(M id) throws Exception;

    /**
     * 根据ID查询
     */
    public T getById(M id) throws Exception;

    /**
     * 【最原始】查询
     */
    public SearchResponse search(SearchRequest searchRequest) throws Exception;


    /**
     * 非分页查询
     */
    public List<T> search(QueryBuilder queryBuilder) throws Exception;


    /**
     * 查询数量
     */
    public long count(QueryBuilder queryBuilder) throws Exception;


    /**
     * 支持分页、高亮、排序的查询
     */
    public PageList<T> search(QueryBuilder queryBuilder, PageSortHighLight pageSortHighLight) throws Exception;

    /**
     * 非分页查询，指定最大返回条数
     */
    public List<T> searchMore(QueryBuilder queryBuilder,int limitSize) throws Exception;

    /**
     * 搜索建议
     */
    public List<String> completionSuggest(String fieldName,String fieldValue) throws Exception;


    /**
     * 普通聚合查询
     * 以bucket分组以aggstypes的方式metric度量
     */
    public Map aggs(String metricName, AggsType aggsType, QueryBuilder queryBuilder, String bucketName) throws Exception;

}
