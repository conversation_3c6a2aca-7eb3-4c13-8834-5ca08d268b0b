package org.zxp.esclientrhl.config;

import org.elasticsearch.common.unit.ByteSizeUnit;

import java.util.concurrent.TimeUnit;

/**
 * 元数据配置类 - 替代@ESMetaData注解
 * 
 * 功能说明：
 * - 完全替代@ESMetaData注解的所有功能
 * - 保持与原注解相同的配置项和默认值
 * - 支持索引、别名、滚动索引等所有高级功能
 * 
 * 使用方式：
 * MetaDataConfig config = new MetaDataConfig("user_index")
 *     .setNumberOfShards(3)
 *     .setNumberOfReplicas(1)
 *     .setAlias(true)
 *     .setRollover(true);
 *
 */
public class MetaDataConfig {
    
    // 基础配置
    private String[] searchIndexNames = {};
    private String indexName;
    private String indexType = "";
    private int numberOfShards = 1;
    private int numberOfReplicas = 1;
    private boolean printLog = false;
    
    // 别名配置
    private boolean alias = false;
    private String[] aliasIndex = {};
    private String writeIndex = "";
    
    // 滚动索引配置
    private boolean rollover = false;
    private boolean autoRollover = false;
    private long autoRolloverInitialDelay = 0L;
    private long autoRolloverPeriod = 4L;
    private TimeUnit autoRolloverTimeUnit = TimeUnit.HOURS;
    private long rolloverMaxIndexAgeCondition = 0L;
    private TimeUnit rolloverMaxIndexAgeTimeUnit = TimeUnit.DAYS;
    private long rolloverMaxIndexDocsCondition = 0L;
    private long rolloverMaxIndexSizeCondition = 0L;
    private ByteSizeUnit rolloverMaxIndexSizeByteSizeUnit = ByteSizeUnit.GB;
    
    // 其他配置
    private long maxResultWindow = 10000L;
    private boolean suffix = false;
    private boolean autoCreateIndex = true;
    private String settingsPath = "";
    private boolean isScore = false;
    
    // 构造函数
    public MetaDataConfig() {}
    
    public MetaDataConfig(String indexName) {
        this.indexName = indexName;
    }
    
    // 链式设置方法
    public MetaDataConfig setSearchIndexNames(String[] searchIndexNames) {
        this.searchIndexNames = searchIndexNames;
        return this;
    }
    
    public MetaDataConfig setIndexName(String indexName) {
        this.indexName = indexName;
        return this;
    }
    
    public MetaDataConfig setIndexType(String indexType) {
        this.indexType = indexType;
        return this;
    }
    
    public MetaDataConfig setNumberOfShards(int numberOfShards) {
        this.numberOfShards = numberOfShards;
        return this;
    }
    
    public MetaDataConfig setNumberOfReplicas(int numberOfReplicas) {
        this.numberOfReplicas = numberOfReplicas;
        return this;
    }
    
    public MetaDataConfig setPrintLog(boolean printLog) {
        this.printLog = printLog;
        return this;
    }
    
    public MetaDataConfig setAlias(boolean alias) {
        this.alias = alias;
        return this;
    }
    
    public MetaDataConfig setAliasIndex(String[] aliasIndex) {
        this.aliasIndex = aliasIndex;
        return this;
    }
    
    public MetaDataConfig setWriteIndex(String writeIndex) {
        this.writeIndex = writeIndex;
        return this;
    }
    
    public MetaDataConfig setRollover(boolean rollover) {
        this.rollover = rollover;
        return this;
    }
    
    public MetaDataConfig setAutoRollover(boolean autoRollover) {
        this.autoRollover = autoRollover;
        return this;
    }
    
    public MetaDataConfig setAutoRolloverInitialDelay(long autoRolloverInitialDelay) {
        this.autoRolloverInitialDelay = autoRolloverInitialDelay;
        return this;
    }
    
    public MetaDataConfig setAutoRolloverPeriod(long autoRolloverPeriod) {
        this.autoRolloverPeriod = autoRolloverPeriod;
        return this;
    }
    
    public MetaDataConfig setAutoRolloverTimeUnit(TimeUnit autoRolloverTimeUnit) {
        this.autoRolloverTimeUnit = autoRolloverTimeUnit;
        return this;
    }
    
    public MetaDataConfig setRolloverMaxIndexAgeCondition(long rolloverMaxIndexAgeCondition) {
        this.rolloverMaxIndexAgeCondition = rolloverMaxIndexAgeCondition;
        return this;
    }
    
    public MetaDataConfig setRolloverMaxIndexAgeTimeUnit(TimeUnit rolloverMaxIndexAgeTimeUnit) {
        this.rolloverMaxIndexAgeTimeUnit = rolloverMaxIndexAgeTimeUnit;
        return this;
    }
    
    public MetaDataConfig setRolloverMaxIndexDocsCondition(long rolloverMaxIndexDocsCondition) {
        this.rolloverMaxIndexDocsCondition = rolloverMaxIndexDocsCondition;
        return this;
    }
    
    public MetaDataConfig setRolloverMaxIndexSizeCondition(long rolloverMaxIndexSizeCondition) {
        this.rolloverMaxIndexSizeCondition = rolloverMaxIndexSizeCondition;
        return this;
    }
    
    public MetaDataConfig setRolloverMaxIndexSizeByteSizeUnit(ByteSizeUnit rolloverMaxIndexSizeByteSizeUnit) {
        this.rolloverMaxIndexSizeByteSizeUnit = rolloverMaxIndexSizeByteSizeUnit;
        return this;
    }
    
    public MetaDataConfig setMaxResultWindow(long maxResultWindow) {
        this.maxResultWindow = maxResultWindow;
        return this;
    }
    
    public MetaDataConfig setSuffix(boolean suffix) {
        this.suffix = suffix;
        return this;
    }
    
    public MetaDataConfig setAutoCreateIndex(boolean autoCreateIndex) {
        this.autoCreateIndex = autoCreateIndex;
        return this;
    }
    
    public MetaDataConfig setSettingsPath(String settingsPath) {
        this.settingsPath = settingsPath;
        return this;
    }
    
    public MetaDataConfig setScore(boolean score) {
        this.isScore = score;
        return this;
    }
    
    // getter方法
    public String[] getSearchIndexNames() { return searchIndexNames; }
    public String getIndexName() { return indexName; }
    public String getIndexType() { return indexType; }
    public int getNumberOfShards() { return numberOfShards; }
    public int getNumberOfReplicas() { return numberOfReplicas; }
    public boolean isPrintLog() { return printLog; }
    public boolean isAlias() { return alias; }
    public String[] getAliasIndex() { return aliasIndex; }
    public String getWriteIndex() { return writeIndex; }
    public boolean isRollover() { return rollover; }
    public boolean isAutoRollover() { return autoRollover; }
    public long getAutoRolloverInitialDelay() { return autoRolloverInitialDelay; }
    public long getAutoRolloverPeriod() { return autoRolloverPeriod; }
    public TimeUnit getAutoRolloverTimeUnit() { return autoRolloverTimeUnit; }
    public long getRolloverMaxIndexAgeCondition() { return rolloverMaxIndexAgeCondition; }
    public TimeUnit getRolloverMaxIndexAgeTimeUnit() { return rolloverMaxIndexAgeTimeUnit; }
    public long getRolloverMaxIndexDocsCondition() { return rolloverMaxIndexDocsCondition; }
    public long getRolloverMaxIndexSizeCondition() { return rolloverMaxIndexSizeCondition; }
    public ByteSizeUnit getRolloverMaxIndexSizeByteSizeUnit() { return rolloverMaxIndexSizeByteSizeUnit; }
    public long getMaxResultWindow() { return maxResultWindow; }
    public boolean isSuffix() { return suffix; }
    public boolean isAutoCreateIndex() { return autoCreateIndex; }
    public String getSettingsPath() { return settingsPath; }
    public boolean isScore() { return isScore; }
    
    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return indexName != null && !indexName.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "MetaDataConfig{" +
                "indexName='" + indexName + '\'' +
                ", indexType='" + indexType + '\'' +
                ", numberOfShards=" + numberOfShards +
                ", numberOfReplicas=" + numberOfReplicas +
                ", alias=" + alias +
                ", rollover=" + rollover +
                '}';
    }
}
