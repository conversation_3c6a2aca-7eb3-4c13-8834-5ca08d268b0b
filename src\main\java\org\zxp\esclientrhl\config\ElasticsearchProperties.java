package org.zxp.esclientrhl.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * Elasticsearch配置属性类
 *
 * 功能说明：
 * - 封装Elasticsearch连接相关的配置参数
 * - 从application.yml或application.properties中读取配置
 * - 提供默认值，简化配置
 *
 * 支持的配置项：
 * - 连接地址和认证信息
 * - 连接池配置
 * - 超时时间配置
 * - 索引后缀配置
 *
 * 配置示例：
 * elasticsearch:
 *   host: 127.0.0.1:9200                    # ES服务地址，支持多个节点用逗号分隔
 *   username: elastic                       # 用户名（可选）
 *   password: password                      # 密码（可选）
 *   max_connect_total: 30                   # 连接池最大连接数
 *   max_connect_per_route: 10               # 每个路由最大连接数
 *   connection_request_timeout_millis: 2000 # 从连接池获取连接的超时时间
 *   socket_timeout_millis: 30000            # Socket读取超时时间
 *   connect_timeout_millis: 2000            # 连接建立超时时间
 *   keep_alive_strategy: -1                 # 连接保活策略
 *   index:
 *     suffix: dev                           # 索引名称后缀
 *
 */
@Component
public class ElasticsearchProperties {
    @Value("${elasticsearch.host:127.0.0.1:9200}")
    private String host;
    @Value("${elasticsearch.username:}")
    private String username;
    @Value("${elasticsearch.password:}")
    private String password;
    /**
     * 连接池里的最大连接数
     */
    @Value("${elasticsearch.max_connect_total:30}")
    private Integer maxConnectTotal;

    /**
     * 某一个/每服务每次能并行接收的请求数量
     */
    @Value("${elasticsearch.max_connect_per_route:10}")
    private Integer maxConnectPerRoute;

    /**
     * http clilent中从connetcion pool中获得一个connection的超时时间
     */
    @Value("${elasticsearch.connection_request_timeout_millis:2000}")
    private Integer connectionRequestTimeoutMillis;

    /**
     * 响应超时时间，超过此时间不再读取响应
     */
    @Value("${elasticsearch.socket_timeout_millis:30000}")
    private Integer socketTimeoutMillis;

    /**
     * 链接建立的超时时间
     */
    @Value("${elasticsearch.connect_timeout_millis:2000}")
    private Integer connectTimeoutMillis;

    /**
     * keep_alive_strategy
     */
    @Value("${elasticsearch.keep_alive_strategy:-1}")
    private Long keepAliveStrategy;


    /**
     * 索引后后缀配置
     */
    @Value("${elasticsearch.index.suffix:}")
    private String suffix;


    public Integer getMaxConnectTotal() {
        return maxConnectTotal;
    }

    public void setMaxConnectTotal(Integer maxConnectTotal) {
        this.maxConnectTotal = maxConnectTotal;
    }

    public Integer getMaxConnectPerRoute() {
        return maxConnectPerRoute;
    }

    public void setMaxConnectPerRoute(Integer maxConnectPerRoute) {
        this.maxConnectPerRoute = maxConnectPerRoute;
    }

    public Integer getConnectionRequestTimeoutMillis() {
        return connectionRequestTimeoutMillis;
    }

    public void setConnectionRequestTimeoutMillis(Integer connectionRequestTimeoutMillis) {
        this.connectionRequestTimeoutMillis = connectionRequestTimeoutMillis;
    }

    public Integer getSocketTimeoutMillis() {
        return socketTimeoutMillis;
    }

    public void setSocketTimeoutMillis(Integer socketTimeoutMillis) {
        this.socketTimeoutMillis = socketTimeoutMillis;
    }

    public Integer getConnectTimeoutMillis() {
        return connectTimeoutMillis;
    }

    public void setConnectTimeoutMillis(Integer connectTimeoutMillis) {
        this.connectTimeoutMillis = connectTimeoutMillis;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }


    public String getSuffix() {
        return suffix;
    }

    public void setSuffix(String suffix) {
        this.suffix = suffix;
    }

    public Long getKeepAliveStrategy() {
        return keepAliveStrategy;
    }

    public ElasticsearchProperties setKeepAliveStrategy(Long keepAliveStrategy) {
        this.keepAliveStrategy = keepAliveStrategy;
        return this;
    }
}
