package org.zxp.esclientrhl.config;

import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.HttpAsyncClientBuilder;
import org.apache.http.ssl.SSLContextBuilder;
import org.elasticsearch.client.RestClientBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.zxp.esclientrhl.util.Constant;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;
import org.springframework.util.StringUtils;

import javax.net.ssl.SSLContext;

/**
 * Elasticsearch自动配置类
 *
 * 功能说明：
 * - 自动配置并创建RestHighLevelClient客户端Bean
 * - 支持集群连接、认证、SSL等高级配置
 * - 扫描并注册ES工具相关的组件
 *
 * 主要功能：
 * 1. 根据配置文件创建ES客户端连接
 * 2. 支持用户名密码认证
 * 3. 支持SSL安全连接
 * 4. 配置连接池参数
 * 5. 扫描org.zxp.esclientrhl包下的所有组件
 *
 * 配置示例（application.yml）：
 * elasticsearch:
 *   host: 127.0.0.1:9200,127.0.0.1:9201  # 支持集群
 *   username: elastic                      # 可选，用户名
 *   password: password                     # 可选，密码
 *   max_connect_total: 30                  # 最大连接数
 *   max_connect_per_route: 10              # 每个路由最大连接数
 *   connection_request_timeout_millis: 2000 # 连接请求超时
 *   socket_timeout_millis: 30000           # Socket超时
 *   connect_timeout_millis: 2000           # 连接超时
 *
 */
@Configuration
@ComponentScan("org.zxp.esclientrhl")
public class ElasticSearchConfiguration {
    private Logger logger = LoggerFactory.getLogger(this.getClass());

    //    @Value("${elasticsearch.host}")
//    private String host;
    @Autowired
    ElasticsearchProperties elasticsearchProperties;

    private RestHighLevelClient restHighLevelClient;

//    由于@Bean(destroyMethod="close")，所以不需要下面注释掉的释放方式
//    public void close() {
//        if (restHighLevelClient != null) {
//            try {
//                restHighLevelClient.close();
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
//    }
//
//    @PreDestroy
//    public void destroyMethod() throws Exception {
//        close();
//    }

    @Bean(destroyMethod = "close")//这个close是调用RestHighLevelClient中的close
    @Scope("singleton")
    public RestHighLevelClient createInstance() {
        String host = elasticsearchProperties.getHost();
        String username = elasticsearchProperties.getUsername();
        String password = elasticsearchProperties.getPassword();
        Integer maxConnectTotal = elasticsearchProperties.getMaxConnectTotal();
        Integer maxConnectPerRoute = elasticsearchProperties.getMaxConnectPerRoute();
        Integer connectionRequestTimeoutMillis = elasticsearchProperties.getConnectionRequestTimeoutMillis();
        Integer socketTimeoutMillis = elasticsearchProperties.getSocketTimeoutMillis();
        Integer connectTimeoutMillis = elasticsearchProperties.getConnectTimeoutMillis();
        Long strategy = elasticsearchProperties.getKeepAliveStrategy();
        try {
            if (StringUtils.isEmpty(host)) {
                host = Constant.DEFAULT_ES_HOST;
            }
            String[] hosts = host.split(",");
            HttpHost[] httpHosts = new HttpHost[hosts.length];
            for (int i = 0; i < httpHosts.length; i++) {
                String h = hosts[i];
                //httpHosts[i] = new HttpHost(h.split(":")[0], Integer.parseInt(h.split(":")[1]), "http");
                httpHosts[i] = HttpHost.create(hosts[i]);
            }

            RestClientBuilder builder = RestClient.builder(httpHosts);
            builder.setRequestConfigCallback(requestConfigBuilder -> {
                requestConfigBuilder.setConnectTimeout(connectTimeoutMillis);
                requestConfigBuilder.setSocketTimeout(socketTimeoutMillis);
                requestConfigBuilder.setConnectionRequestTimeout(connectionRequestTimeoutMillis);
                return requestConfigBuilder;
            });
            // 创建SSLContext以跳过SSL证书验证
            SSLContext sslContext = SSLContextBuilder.create()
                    .loadTrustMaterial((chain, authType) -> true)
                    .build();
            if (!StringUtils.isEmpty(username)) {
                final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
                credentialsProvider.setCredentials(AuthScope.ANY,
                        new UsernamePasswordCredentials(username, password));  //es账号密码（默认用户名为elastic）

                builder.setHttpClientConfigCallback(httpClientBuilder -> {
                    httpClientBuilder.disableAuthCaching();
                    httpClientBuilder.setMaxConnTotal(maxConnectTotal);
                    httpClientBuilder.setMaxConnPerRoute(maxConnectPerRoute);
                    httpClientBuilder.setDefaultCredentialsProvider(credentialsProvider);
                    if (strategy > 0){
                        httpClientBuilder.setKeepAliveStrategy((httpResponse, httpContext) -> strategy);
                    }
                    httpClientBuilder.setSSLContext(sslContext);
                    return httpClientBuilder;
                });
            } else {
                builder.setHttpClientConfigCallback(httpClientBuilder -> {
                    httpClientBuilder.disableAuthCaching();
                    httpClientBuilder.setMaxConnTotal(maxConnectTotal);
                    httpClientBuilder.setMaxConnPerRoute(maxConnectPerRoute);
                    if (strategy > 0){
                        httpClientBuilder.setKeepAliveStrategy((httpResponse, httpContext) -> strategy);
                    }
                    httpClientBuilder.setSSLContext(sslContext);
                    return httpClientBuilder;
                });
            }

            restHighLevelClient = new RestHighLevelClient(builder);
        } catch (Exception e) {
            logger.error("create RestHighLevelClient error", e);
            return null;
        }
        return restHighLevelClient;
    }
}
