package org.zxp.esclientrhl.example;

import org.zxp.esclientrhl.config.EntityConfig;
import org.zxp.esclientrhl.config.MappingConfig;
import org.zxp.esclientrhl.config.MetaDataConfig;
import org.zxp.esclientrhl.enums.Analyzer;
import org.zxp.esclientrhl.enums.DataType;
import org.zxp.esclientrhl.util.IndexTools;
import org.zxp.esclientrhl.util.MappingData;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Date;

/**
 * IndexTools修复验证测试
 * 
 * 验证IndexTools中注解依赖清理的修复：
 * 1. 验证getMappingData方法支持EntityConfig
 * 2. 验证注解依赖完全清理
 * 3. 验证默认类型推断逻辑
 * 4. 验证MappingConfig新增方法
 * 
 * <AUTHOR> zhang
 * @since 2024-01-01
 */
public class IndexToolsFixValidationTest {
    
    public static void main(String[] args) {
        try {
            System.out.println("=== IndexTools修复验证测试 ===");
            
            // 1. 创建测试实体配置
            EntityConfig userConfig = createTestEntityConfig();
            
            // 2. 创建IndexTools实例
            IndexTools indexTools = new IndexTools();
            
            // 3. 测试getMappingData方法（EntityConfig方式）
            testGetMappingDataWithConfig(indexTools, userConfig);
            
            // 4. 测试getMappingData方法（默认方式）
            testGetMappingDataDefault(indexTools);
            
            // 5. 测试MappingConfig新增方法
            testMappingConfigNewMethods();
            
            // 6. 测试类型推断逻辑
            testTypeInference(indexTools);
            
            System.out.println("\n=== IndexTools修复验证测试通过！注解依赖完全清理！ ===");
            
        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建测试实体配置
     */
    private static EntityConfig createTestEntityConfig() {
        System.out.println("\n1. 创建测试实体配置");
        
        MetaDataConfig metaData = new MetaDataConfig("indextools_test")
            .setNumberOfShards(1)
            .setNumberOfReplicas(0)
            .setPrintLog(true)
            .setScore(true)
            .setAutoCreateIndex(true);
        
        EntityConfig entityConfig = new EntityConfig(User.class)
            .setMetaData(metaData)
            .setIdField("id")
            .setScoreField("score");
        
        // 添加字段映射配置
        entityConfig
            .addFieldMapping("name", new MappingConfig()
                .setDatatype(DataType.text_type)
                .setAnalyzer(Analyzer.ik_max_word)
                .setKeyword(true)
                .setNgram(true)
                .setDateFormat(Arrays.asList("yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss")))
            .addFieldMapping("age", new MappingConfig()
                .setDatatype(DataType.integer_type))
            .addFieldMapping("email", new MappingConfig()
                .setDatatype(DataType.keyword_type)
                .setNormalizer("lowercase"))
            .addFieldMapping("description", new MappingConfig()
                .setDatatype(DataType.text_type)
                .setAnalyzer(Analyzer.ik_max_word)
                .setNullValue("默认描述"));
        
        System.out.println("✓ 测试实体配置创建成功");
        return entityConfig;
    }
    
    /**
     * 测试getMappingData方法（EntityConfig方式）
     */
    private static void testGetMappingDataWithConfig(IndexTools indexTools, EntityConfig userConfig) throws Exception {
        System.out.println("\n2. 测试getMappingData方法（EntityConfig方式）");
        
        // 获取User类的字段
        Field nameField = User.class.getDeclaredField("name");
        Field ageField = User.class.getDeclaredField("age");
        Field emailField = User.class.getDeclaredField("email");
        Field descriptionField = User.class.getDeclaredField("description");
        
        // 测试name字段映射
        MappingData nameMapping = indexTools.getMappingData(nameField, userConfig);
        System.out.println("✓ name字段映射:");
        System.out.println("  - 数据类型: " + nameMapping.getDatatype());
        System.out.println("  - 分析器: " + nameMapping.getAnalyzer());
        System.out.println("  - 关键字: " + nameMapping.isKeyword());
        System.out.println("  - N-gram: " + nameMapping.isNgram());
        System.out.println("  - 日期格式: " + nameMapping.getDateFormat());
        
        // 测试age字段映射
        MappingData ageMapping = indexTools.getMappingData(ageField, userConfig);
        System.out.println("✓ age字段映射:");
        System.out.println("  - 数据类型: " + ageMapping.getDatatype());
        
        // 测试email字段映射
        MappingData emailMapping = indexTools.getMappingData(emailField, userConfig);
        System.out.println("✓ email字段映射:");
        System.out.println("  - 数据类型: " + emailMapping.getDatatype());
        System.out.println("  - 标准化器: " + emailMapping.getNormalizer());
        
        // 测试description字段映射
        MappingData descriptionMapping = indexTools.getMappingData(descriptionField, userConfig);
        System.out.println("✓ description字段映射:");
        System.out.println("  - 数据类型: " + descriptionMapping.getDatatype());
        System.out.println("  - 分析器: " + descriptionMapping.getAnalyzer());
        System.out.println("  - 空值: " + descriptionMapping.getNull_value());
        
        System.out.println("✓ EntityConfig方式测试通过");
    }
    
    /**
     * 测试getMappingData方法（默认方式）
     */
    private static void testGetMappingDataDefault(IndexTools indexTools) throws Exception {
        System.out.println("\n3. 测试getMappingData方法（默认方式）");
        
        // 获取User类的字段
        Field nameField = User.class.getDeclaredField("name");
        Field ageField = User.class.getDeclaredField("age");
        Field createTimeField = User.class.getDeclaredField("createTime");
        
        // 测试默认映射（不使用EntityConfig）
        MappingData nameMapping = indexTools.getMappingData(nameField);
        System.out.println("✓ name字段默认映射:");
        System.out.println("  - 数据类型: " + nameMapping.getDatatype());
        System.out.println("  - 分析器: " + nameMapping.getAnalyzer());
        System.out.println("  - 关键字: " + nameMapping.isKeyword());
        
        MappingData ageMapping = indexTools.getMappingData(ageField);
        System.out.println("✓ age字段默认映射:");
        System.out.println("  - 数据类型: " + ageMapping.getDatatype());
        
        MappingData createTimeMapping = indexTools.getMappingData(createTimeField);
        System.out.println("✓ createTime字段默认映射:");
        System.out.println("  - 数据类型: " + createTimeMapping.getDatatype());
        
        System.out.println("✓ 默认方式测试通过");
    }
    
    /**
     * 测试MappingConfig新增方法
     */
    private static void testMappingConfigNewMethods() {
        System.out.println("\n4. 测试MappingConfig新增方法");
        
        MappingConfig config = new MappingConfig()
            .setDatatype(DataType.text_type)
            .setAnalyzer(Analyzer.ik_max_word)
            .setNgram(true)
            .setDateFormat(Arrays.asList("yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss"))
            .setNormalizer("lowercase")
            .setNullValue("默认值");
        
        // 测试新增的getter方法
        System.out.println("✓ isNgram(): " + config.isNgram());
        System.out.println("✓ getDateFormat(): " + config.getDateFormat());
        System.out.println("✓ getNormalizer(): " + config.getNormalizer());
        System.out.println("✓ getNullValue(): " + config.getNullValue());
        System.out.println("✓ isAllowSearch(): " + config.isAllowSearch());
        System.out.println("✓ getCopyTo(): " + config.getCopyTo());
        
        System.out.println("✓ MappingConfig新增方法测试通过");
    }
    
    /**
     * 测试类型推断逻辑
     */
    private static void testTypeInference(IndexTools indexTools) throws Exception {
        System.out.println("\n5. 测试类型推断逻辑");
        
        // 创建测试类
        TestTypeClass testObj = new TestTypeClass();
        
        // 测试各种类型的字段
        Field stringField = TestTypeClass.class.getDeclaredField("stringField");
        Field intField = TestTypeClass.class.getDeclaredField("intField");
        Field longField = TestTypeClass.class.getDeclaredField("longField");
        Field doubleField = TestTypeClass.class.getDeclaredField("doubleField");
        Field booleanField = TestTypeClass.class.getDeclaredField("booleanField");
        Field dateField = TestTypeClass.class.getDeclaredField("dateField");
        
        System.out.println("✓ 类型推断结果:");
        System.out.println("  - String -> " + indexTools.getMappingData(stringField).getDatatype());
        System.out.println("  - int -> " + indexTools.getMappingData(intField).getDatatype());
        System.out.println("  - long -> " + indexTools.getMappingData(longField).getDatatype());
        System.out.println("  - double -> " + indexTools.getMappingData(doubleField).getDatatype());
        System.out.println("  - boolean -> " + indexTools.getMappingData(booleanField).getDatatype());
        System.out.println("  - Date -> " + indexTools.getMappingData(dateField).getDatatype());
        
        System.out.println("✓ 类型推断逻辑测试通过");
    }
    
    /**
     * 测试类型推断的测试类
     */
    public static class TestTypeClass {
        private String stringField;
        private int intField;
        private long longField;
        private double doubleField;
        private boolean booleanField;
        private Date dateField;
        
        // getter和setter方法
        public String getStringField() { return stringField; }
        public void setStringField(String stringField) { this.stringField = stringField; }
        public int getIntField() { return intField; }
        public void setIntField(int intField) { this.intField = intField; }
        public long getLongField() { return longField; }
        public void setLongField(long longField) { this.longField = longField; }
        public double getDoubleField() { return doubleField; }
        public void setDoubleField(double doubleField) { this.doubleField = doubleField; }
        public boolean isBooleanField() { return booleanField; }
        public void setBooleanField(boolean booleanField) { this.booleanField = booleanField; }
        public Date getDateField() { return dateField; }
        public void setDateField(Date dateField) { this.dateField = dateField; }
    }
}
