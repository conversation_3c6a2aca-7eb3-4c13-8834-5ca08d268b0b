package org.zxp.esclientrhl.util;

/**
 * ES工具常量类
 *
 * 功能说明：
 * - 定义ES操作相关的默认值和常量
 * - 提供框架内部使用的配置参数
 * - 统一管理默认配置，便于维护和修改
 *
 * 主要常量分类：
 * 1. 查询相关：默认分页大小、搜索建议条数等
 * 2. 高亮相关：默认高亮标签
 * 3. 索引相关：默认ES主机地址、是否创建keyword字段等
 * 4. 滚动查询：默认滚动时间和每页条数
 * 5. 聚合查询：默认百分比查询规格、聚合结果最大条数
 * 6. 批量操作：批量更新每批次条数
 *
 * 使用说明：
 * - 这些常量可以通过配置文件覆盖
 * - 主要用于提供合理的默认值
 * - 避免硬编码，提高代码可维护性
 */
public class Constant {
    //非分页，默认的查询条数
    public static int DEFALT_PAGE_SIZE = 200;
    //搜索建议默认条数
    public static int COMPLETION_SUGGESTION_SIZE = 10;
    //高亮字段默认tag
    public static String HIGHLIGHT_TAG = "";
    //创建索引mapping时，是否默认创建keyword
    public static boolean DEFAULT_KEYWORDS = true;

    public static String DEFAULT_ES_HOST = "127.0.0.1:9200";
    //SCROLL查询 2小时
    public static long DEFAULT_SCROLL_TIME = 2;
    //SCROLL查询 每页默认条数
    public static int DEFAULT_SCROLL_PERPAGE = 100;
    //默认百分比查询规格
    public static double[] DEFAULT_PERCSEGMENT = {50.0,95.0,99.0};

    //批量更新（新增）每批次条数
    public static int BULK_COUNT = 5000;

    //聚合查询返回最大条数
    public static int AGG_RESULT_COUNT = Integer.MAX_VALUE;

    public static long DEFAULT_PRECISION_THRESHOLD = 3000L;
 }
