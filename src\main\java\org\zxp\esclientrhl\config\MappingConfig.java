package org.zxp.esclientrhl.config;

import org.zxp.esclientrhl.enums.Analyzer;
import org.zxp.esclientrhl.enums.DataType;

import java.util.List;

/**
 * 字段映射配置类 - 替代@ESMapping注解
 * 
 * 功能说明：
 * - 完全替代@ESMapping注解的所有功能
 * - 保持与原注解相同的配置项和默认值
 * - 支持所有ES字段类型和分析器配置
 * 
 * 使用方式：
 * MappingConfig nameMapping = new MappingConfig()
 *     .setDatatype(DataType.text_type)
 *     .setAnalyzer(Analyzer.ik_max_word)
 *     .setKeyword(true);
 *
 */
public class MappingConfig {
    
    private DataType datatype = DataType.text_type;
    private Analyzer analyzer = Analyzer.standard;
    private Analyzer searchAnalyzer = Analyzer.standard;
    private boolean allowSearch = true;
    private boolean allowSort = true;
    private boolean keyword = false;
    private boolean suggest = false;
    private boolean copy = false;
    private String copyTo = "";
    private String value = "";
    private String format = "";
    private String normalizer = "";
    private boolean store = false;
    private boolean includeInAll = true;
    private int ignoreAbove = 256;
    private boolean docValues = true;
    private boolean norms = true;
    private String indexOptions = "";
    private String similarity = "";
    private String termVector = "";
    private double boost = 1.0;
    private String nullValue = "";
    private int positionIncrementGap = 100;
    private boolean fielddata = false;
    private boolean eagerGlobalOrdinals = false;
    private String indexPhrases = "";
    private String indexPrefixes = "";
    private boolean ngram = false;
    private List<String> dateFormat;

    // 构造函数
    public MappingConfig() {}
    
    public MappingConfig(DataType datatype) {
        this.datatype = datatype;
    }
    
    // 链式设置方法
    public MappingConfig setDatatype(DataType datatype) {
        this.datatype = datatype;
        return this;
    }
    
    public MappingConfig setAnalyzer(Analyzer analyzer) {
        this.analyzer = analyzer;
        return this;
    }
    
    public MappingConfig setSearchAnalyzer(Analyzer searchAnalyzer) {
        this.searchAnalyzer = searchAnalyzer;
        return this;
    }
    
    public MappingConfig setAllowSearch(boolean allowSearch) {
        this.allowSearch = allowSearch;
        return this;
    }
    
    public MappingConfig setAllowSort(boolean allowSort) {
        this.allowSort = allowSort;
        return this;
    }
    
    public MappingConfig setKeyword(boolean keyword) {
        this.keyword = keyword;
        return this;
    }
    
    public MappingConfig setSuggest(boolean suggest) {
        this.suggest = suggest;
        return this;
    }
    
    public MappingConfig setCopy(boolean copy) {
        this.copy = copy;
        return this;
    }
    
    public MappingConfig setCopyTo(String copyTo) {
        this.copyTo = copyTo;
        return this;
    }
    
    public MappingConfig setValue(String value) {
        this.value = value;
        return this;
    }
    
    public MappingConfig setFormat(String format) {
        this.format = format;
        return this;
    }
    
    public MappingConfig setNormalizer(String normalizer) {
        this.normalizer = normalizer;
        return this;
    }
    
    public MappingConfig setStore(boolean store) {
        this.store = store;
        return this;
    }
    
    public MappingConfig setIncludeInAll(boolean includeInAll) {
        this.includeInAll = includeInAll;
        return this;
    }
    
    public MappingConfig setIgnoreAbove(int ignoreAbove) {
        this.ignoreAbove = ignoreAbove;
        return this;
    }
    
    public MappingConfig setDocValues(boolean docValues) {
        this.docValues = docValues;
        return this;
    }
    
    public MappingConfig setNorms(boolean norms) {
        this.norms = norms;
        return this;
    }
    
    public MappingConfig setIndexOptions(String indexOptions) {
        this.indexOptions = indexOptions;
        return this;
    }
    
    public MappingConfig setSimilarity(String similarity) {
        this.similarity = similarity;
        return this;
    }
    
    public MappingConfig setTermVector(String termVector) {
        this.termVector = termVector;
        return this;
    }
    
    public MappingConfig setBoost(double boost) {
        this.boost = boost;
        return this;
    }
    
    public MappingConfig setNullValue(String nullValue) {
        this.nullValue = nullValue;
        return this;
    }
    
    public MappingConfig setPositionIncrementGap(int positionIncrementGap) {
        this.positionIncrementGap = positionIncrementGap;
        return this;
    }
    
    public MappingConfig setFielddata(boolean fielddata) {
        this.fielddata = fielddata;
        return this;
    }
    
    public MappingConfig setEagerGlobalOrdinals(boolean eagerGlobalOrdinals) {
        this.eagerGlobalOrdinals = eagerGlobalOrdinals;
        return this;
    }
    
    public MappingConfig setIndexPhrases(String indexPhrases) {
        this.indexPhrases = indexPhrases;
        return this;
    }
    
    public MappingConfig setIndexPrefixes(String indexPrefixes) {
        this.indexPrefixes = indexPrefixes;
        return this;
    }

    public MappingConfig setNgram(boolean ngram) {
        this.ngram = ngram;
        return this;
    }

    public MappingConfig setDateFormat(List<String> dateFormat) {
        this.dateFormat = dateFormat;
        return this;
    }

    // getter方法
    public DataType getDatatype() { return datatype; }
    public Analyzer getAnalyzer() { return analyzer; }
    public Analyzer getSearchAnalyzer() { return searchAnalyzer; }
    public boolean isAllowSearch() { return allowSearch; }
    public boolean isAllowSort() { return allowSort; }
    public boolean isKeyword() { return keyword; }
    public boolean isSuggest() { return suggest; }
    public boolean isCopy() { return copy; }
    public String getCopyTo() { return copyTo; }
    public String getValue() { return value; }
    public String getFormat() { return format; }
    public String getNormalizer() { return normalizer; }
    public boolean isStore() { return store; }
    public boolean isIncludeInAll() { return includeInAll; }
    public int getIgnoreAbove() { return ignoreAbove; }
    public boolean isDocValues() { return docValues; }
    public boolean isNorms() { return norms; }
    public String getIndexOptions() { return indexOptions; }
    public String getSimilarity() { return similarity; }
    public String getTermVector() { return termVector; }
    public double getBoost() { return boost; }
    public String getNullValue() { return nullValue; }
    public int getPositionIncrementGap() { return positionIncrementGap; }
    public boolean isFielddata() { return fielddata; }
    public boolean isEagerGlobalOrdinals() { return eagerGlobalOrdinals; }
    public String getIndexPhrases() { return indexPhrases; }
    public String getIndexPrefixes() { return indexPrefixes; }
    public boolean isNgram() { return ngram; }
    public List<String> getDateFormat() { return dateFormat; }

    @Override
    public String toString() {
        return "MappingConfig{" +
                "datatype=" + datatype +
                ", analyzer=" + analyzer +
                ", keyword=" + keyword +
                ", suggest=" + suggest +
                '}';
    }
}
