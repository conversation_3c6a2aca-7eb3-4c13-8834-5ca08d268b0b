package org.zxp.esclientrhl.config;

import java.util.HashMap;
import java.util.Map;

/**
 * 实体配置类 - 替代注解配置
 * 
 * 功能说明：
 * - 整合MetaDataConfig和字段映射配置
 * - 替代实体类上的所有注解配置
 * - 支持ID字段、评分字段的配置
 * - 提供完整的实体映射信息
 * 
 * 使用方式：
 * EntityConfig userConfig = new EntityConfig()
 *     .setMetaData(new MetaDataConfig("user_index"))
 *     .setIdField("id")
 *     .addFieldMapping("name", new MappingConfig(DataType.text_type))
 *     .addFieldMapping("age", new MappingConfig(DataType.integer_type));
 */
public class EntityConfig {
    
    private MetaDataConfig metaData;
    private Map<String, MappingConfig> fieldMappings;
    private String idField;
    private String scoreField;
    private Class<?> entityClass;
    
    // 构造函数
    public EntityConfig() {
        this.fieldMappings = new HashMap<>();
    }
    
    public EntityConfig(Class<?> entityClass) {
        this.entityClass = entityClass;
        this.fieldMappings = new HashMap<>();
    }
    
    public EntityConfig(MetaDataConfig metaData) {
        this.metaData = metaData;
        this.fieldMappings = new HashMap<>();
    }
    
    // 链式设置方法
    public EntityConfig setMetaData(MetaDataConfig metaData) {
        this.metaData = metaData;
        return this;
    }
    
    public EntityConfig setEntityClass(Class<?> entityClass) {
        this.entityClass = entityClass;
        return this;
    }
    
    public EntityConfig setIdField(String idField) {
        this.idField = idField;
        return this;
    }
    
    public EntityConfig setScoreField(String scoreField) {
        this.scoreField = scoreField;
        return this;
    }
    
    public EntityConfig addFieldMapping(String fieldName, MappingConfig mappingConfig) {
        this.fieldMappings.put(fieldName, mappingConfig);
        return this;
    }
    
    public EntityConfig setFieldMappings(Map<String, MappingConfig> fieldMappings) {
        this.fieldMappings = fieldMappings;
        return this;
    }
    
    // getter方法
    public MetaDataConfig getMetaData() { return metaData; }
    public Map<String, MappingConfig> getFieldMappings() { return fieldMappings; }
    public String getIdField() { return idField; }
    public String getScoreField() { return scoreField; }
    public Class<?> getEntityClass() { return entityClass; }
    
    /**
     * 获取指定字段的映射配置
     */
    public MappingConfig getFieldMapping(String fieldName) {
        return fieldMappings.get(fieldName);
    }
    
    /**
     * 检查字段是否有映射配置
     */
    public boolean hasFieldMapping(String fieldName) {
        return fieldMappings.containsKey(fieldName);
    }

    /**
     * 检查是否有字段映射配置
     */
    public boolean hasFieldMappings() {
        return fieldMappings != null && !fieldMappings.isEmpty();
    }

    /**
     * 检查是否有ID字段配置
     */
    public boolean hasIdField() {
        return idField != null && !idField.trim().isEmpty();
    }
    
    /**
     * 检查是否有评分字段配置
     */
    public boolean hasScoreField() {
        return scoreField != null && !scoreField.trim().isEmpty();
    }
    
    /**
     * 验证配置是否有效
     */
    public boolean isValid() {
        return metaData != null && metaData.isValid();
    }
    
    /**
     * 获取索引名称
     */
    public String getIndexName() {
        return metaData != null ? metaData.getIndexName() : null;
    }
    
    /**
     * 获取索引类型
     */
    public String getIndexType() {
        return metaData != null ? metaData.getIndexType() : "_doc";
    }
    
    /**
     * 是否启用评分
     */
    public boolean isScoreEnabled() {
        return metaData != null && metaData.isScore();
    }
    
    /**
     * 是否自动创建索引
     */
    public boolean isAutoCreateIndex() {
        return metaData != null && metaData.isAutoCreateIndex();
    }
    
    /**
     * 是否使用别名
     */
    public boolean isAlias() {
        return metaData != null && metaData.isAlias();
    }
    
    /**
     * 是否启用滚动索引
     */
    public boolean isRollover() {
        return metaData != null && metaData.isRollover();
    }
    
    /**
     * 创建一个副本
     */
    public EntityConfig copy() {
        EntityConfig copy = new EntityConfig();
        copy.metaData = this.metaData;
        copy.fieldMappings = new HashMap<>(this.fieldMappings);
        copy.idField = this.idField;
        copy.scoreField = this.scoreField;
        copy.entityClass = this.entityClass;
        return copy;
    }
    
    @Override
    public String toString() {
        return "EntityConfig{" +
                "metaData=" + metaData +
                ", fieldMappings=" + fieldMappings.size() + " fields" +
                ", idField='" + idField + '\'' +
                ", scoreField='" + scoreField + '\'' +
                ", entityClass=" + (entityClass != null ? entityClass.getSimpleName() : "null") +
                '}';
    }
}
